:root {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* TC Wehen Colors - Logo-inspired softer palette */
  --primary-red: #c53030;
  --primary-orange: #dd6b20;
  --accent-red: #e53e3e;
  --accent-orange: #ed8936;
  --logo-blue: #2b6cb0;
  --logo-green: #38a169;
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Gradients - Softer and more balanced */
  --gradient-primary: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-orange) 50%, var(--logo-blue) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-orange) 100%);
  --gradient-light: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);

  color: var(--gray-900);
  background-color: var(--white);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--gray-50);
}

a {
  font-weight: 500;
  color: var(--primary-red);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-red);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--gray-900);
  font-weight: 600;
}

.btn {
  border-radius: 8px;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
}

.btn-secondary:hover {
  background-color: var(--primary-red);
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: var(--gray-600);
  border: 1px solid var(--gray-300);
}

.btn-outline:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
}

.card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--gray-200);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--gray-700);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .hidden {
    display: none;
  }

  .md\\:flex {
    display: flex;
  }
}

@media (max-width: 767px) {
  .md\\:hidden {
    display: none;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Mobile optimizations for loading and auth screens */
@media (max-width: 768px) {
  /* Loading screen mobile */
  .loading-screen {
    padding: 1rem;
  }

  .loading-logo {
    width: 100px !important;
    height: 100px !important;
  }

  .loading-logo img {
    width: 75px !important;
    height: 75px !important;
  }

  .loading-title {
    font-size: 1.5rem !important;
  }

  /* Auth page mobile */
  .card {
    margin: 1rem !important;
    padding: 1.5rem !important;
    max-width: 350px !important;
  }

  .auth-logo {
    width: 70px !important;
    height: 70px !important;
  }

  .auth-logo img {
    width: 50px !important;
    height: 50px !important;
  }
}

@media (max-width: 480px) {
  .loading-screen {
    padding: 0.5rem;
  }

  .loading-logo {
    width: 80px !important;
    height: 80px !important;
    margin-bottom: 1.5rem !important;
  }

  .loading-logo img {
    width: 60px !important;
    height: 60px !important;
  }

  .loading-title {
    font-size: 1.3rem !important;
  }

  .card {
    margin: 0.5rem !important;
    padding: 1rem !important;
    max-width: 320px !important;
  }
}
