import React, { useState, useEffect } from 'react';
import './LandingPage.css';

const LandingPage = ({ onBookingClick }) => {
  const [currentPage, setCurrentPage] = useState('home');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Scroll-Handler für Auto-Hide/Show Header
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down & past 100px
        setIsHeaderVisible(false);
        setIsMobileMenuOpen(false); // Close mobile menu when scrolling down
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up
        setIsHeaderVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const navigate = (page) => {
    // Handle external redirects
    if (page === 'medenspiele') {
      window.open('https://htv.liga.nu/cgi-bin/WebObjects/nuLigaTENDE.woa/wa/clubMeetings?club=24972', '_blank');
      return;
    }
    
    if (page === 'mitglied-werden') {
      window.open('http://tc-wehen.de/wp-content/uploads/2025/04/Beitragsordnung_TCWehen_2025.pdf', '_blank');
      return;
    }
    
    if (page === 'satzung') {
      window.open('https://tc-wehen.com/wp-content/uploads/2021/03/Satzung_TC-Wehen_Maerz_2020.pdf', '_blank');
      return;
    }
    
    if (page === 'hand-spanndienst') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/04/TCW_HSD_Arbeitsstunden.pdf', '_blank');
      return;
    }
    
    if (page === 'mach-mit') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/01/Mithilfe_Mitglieder.pdf', '_blank');
      return;
    }

    if (page === 'buchung') {
      onBookingClick();
      return;
    }

    setCurrentPage(page);
    setIsMobileMenuOpen(false); // Close mobile menu when navigating
    setIsDropdownOpen(false); // Close dropdown when navigating
  };

  const toggleDropdown = (e) => {
    e.preventDefault();
    setIsDropdownOpen(!isDropdownOpen);
  };

  const Header = () => (
    <header className={`header ${isHeaderVisible ? 'header-visible' : 'header-hidden'}`}>
      <div className="header-content">
        <div className="logo">
          <img src="/assets/Logo-TCW.PNG" alt="TC-Wehen Logo" className="logo-img" />
          <h1>TC-Wehen</h1>
        </div>

        {/* Hamburger Menu Button */}
        <button
          className="mobile-menu-toggle"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Menu"
        >
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
        </button>

        <nav className={`nav ${isMobileMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-list">
            <li><a href="#" onClick={() => navigate('home')} className={currentPage === 'home' ? 'active' : ''}>Home</a></li>
            <li><a href="#" onClick={() => navigate('training')} className={currentPage === 'training' ? 'active' : ''}>Training</a></li>
            <li><a href="#" onClick={() => navigate('buchung')} className="booking-button">Platz buchen</a></li>
            <li className={`dropdown ${isDropdownOpen ? 'dropdown-open' : ''}`}>
              <a href="#" onClick={toggleDropdown} className={currentPage === 'verein' ? 'active' : ''}>
                Verein <span className="dropdown-arrow">▼</span>
              </a>
              <ul className="dropdown-menu">
                <li><a href="#" onClick={() => navigate('verein')}>Verein Info</a></li>
                <li><a href="#" onClick={() => navigate('verein-anlage')}>Unsere Anlage</a></li>
                <li><a href="#" onClick={() => navigate('mitglied-werden')}>Mitglied werden</a></li>
                <li><a href="#" onClick={() => navigate('satzung')}>Satzung</a></li>
                <li><a href="#" onClick={() => navigate('hand-spanndienst')}>Hand- und Spanndienst</a></li>
                <li><a href="#" onClick={() => navigate('mach-mit')}>Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onClick={() => navigate('medenspiele')} className={currentPage === 'medenspiele' ? 'active' : ''}>Medenspiele</a></li>
            <li><a href="#" onClick={() => navigate('kontakt')} className={currentPage === 'kontakt' ? 'active' : ''}>Kontakt</a></li>
          </ul>
        </nav>
      </div>
    </header>
  );

  const HomePage = () => (
    <div className="page home-page" style={{backgroundImage: "url('/assets/Plätze.PNG')"}}>
      <div className="home-content">
        <div className="welcome-message">
          <h2>Herzlich Willkommen beim TC-Wehen!</h2>
          <p className="welcome-subtitle">Ihr Tennisverein in Wehen mit 6 gepflegten Sandplätzen</p>
        </div>

        <div className="news-section">
          <h3>Aktuelle News</h3>
          <div className="news-grid">
            <div className="news-item">
              <div className="news-date">15. Dez 2024</div>
              <h4>Winterpause beendet</h4>
              <p>Ab sofort sind unsere Plätze wieder geöffnet! Wir freuen uns auf die neue Saison.</p>
            </div>
            <div className="news-item">
              <div className="news-date">10. Dez 2024</div>
              <h4>Neue Buchungszeiten</h4>
              <p>Das Online-Buchungssystem ist jetzt verfügbar. Buchen Sie Ihren Platz bequem von zu Hause.</p>
            </div>
            <div className="news-item">
              <div className="news-date">5. Dez 2024</div>
              <h4>Tennisschule Prätorius</h4>
              <p>Professionelles Training für alle Altersgruppen. Kontaktieren Sie Frank Prätorius für weitere Informationen.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const TrainingPage = () => (
    <div className="page training-page" style={{backgroundImage: "url('/assets/Plätze3.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="training-content">
          <img src="/assets/Tennisschule-Prätorius.PNG" alt="Tennisschule Prätorius" className="training-logo" />
          <div className="trainer-placeholder">
            <div className="trainer-image-placeholder">
              [Platzhalter für Bild von Frank Prätorius]
            </div>
            <p className="training-message">Professionelles Tennistraining für alle Altersgruppen und Spielstärken!</p>
            <div className="contact-info">
              <p><strong>Kontakt:</strong></p>
              <p>Email: Beispiel.Frank@Prätorius.TC-Wehen</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const VereinPage = () => (
    <div className="page verein-page" style={{backgroundImage: "url('/assets/Plätze2.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="verein-content">
          <h2>Unser Verein</h2>
          <div className="vorstand-placeholder">
            [Platzhalter für Gruppenfoto des Vorstandes]
          </div>
          <div className="vorstand-liste">
            <h3>Vorstand:</h3>
            <ul>
              <li>1. Vorsitzender: [Name]</li>
              <li>2. Vorsitzender: [Name]</li>
              <li>Kassenwart: [Name]</li>
              <li>Schriftführer: [Name]</li>
              <li>Sportwart: [Name]</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const AnlagePage = () => (
    <div className="page anlage-page" style={{backgroundImage: "url('/assets/Obenansicht-Plätze.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="anlage-content">
          <h2>Unsere Anlage</h2>
          <div className="anlage-images">
            <img src="/assets/Plätze.PNG" alt="Tennisplätze" />
            <img src="/assets/Plätze2.PNG" alt="Tennisplätze 2" />
            <img src="/assets/Plätze3.PNG" alt="Tennisplätze 3" />
            <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Terrassenansicht" />
            <img src="/assets/Obenansicht-Plätze.PNG" alt="Obenansicht" />
          </div>
          <p>Hier beim TC-Wehen haben wir 6 schöne und gepflegte Sandplätze und eine Ballwand. Sonne lässt sich bis zum Abend wunderschön auf unserer Terrasse bewundern! Kommt doch mal vorbei!</p>
          <div className="google-maps">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2515.123456789!2d8.123456!3d50.123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2sTC%20Wehen!5e0!3m2!1sde!2sde!4v1234567890"
              width="100%"
              height="300"
              style={{border: 0}}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="TC-Wehen Standort"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );

  const KontaktPage = () => (
    <div className="page kontakt-page" style={{backgroundImage: "url('/assets/Ball.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="kontakt-content">
          <h2>Kontakt</h2>
          <div className="kontakt-info">
            <p><strong>Telefon:</strong> [Platzhalter Telefonnummer]</p>
            <p><strong>Email:</strong> [Platzhalter Club Email]</p>
          </div>
        </div>
      </div>
    </div>
  );

  const ImpressumPage = () => (
    <div className="page impressum-page" style={{backgroundImage: "url('/assets/Plätze.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="impressum-content">
          <h2>Impressum</h2>
          <div className="impressum-info">
            <h3>Angaben gemäß § 5 TMG</h3>
            <p><strong>TC-Wehen e.V.</strong></p>
            <p>[Vereinsadresse]</p>
            <p>[PLZ Ort]</p>

            <h3>Vertreten durch:</h3>
            <p>1. Vorsitzender: [Name]</p>
            <p>2. Vorsitzender: [Name]</p>

            <h3>Kontakt:</h3>
            <p>Telefon: [Telefonnummer]</p>
            <p>E-Mail: [E-Mail-Adresse]</p>

            <h3>Registereintrag:</h3>
            <p>Eintragung im Vereinsregister</p>
            <p>Registergericht: [Amtsgericht]</p>
            <p>Registernummer: [VR-Nummer]</p>

            <h3>Verantwortlich für den Inhalt nach § 55 Abs. 2 RStV:</h3>
            <p>[Name des Verantwortlichen]</p>
            <p>[Adresse]</p>
          </div>
        </div>
      </div>
    </div>
  );

  const DatenschutzPage = () => (
    <div className="page datenschutz-page" style={{backgroundImage: "url('/assets/Terrassenansicht-Plätze.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="datenschutz-content">
          <h2>Datenschutzerklärung</h2>
          <div className="datenschutz-info">
            <h3>1. Datenschutz auf einen Blick</h3>
            <p>Die folgenden Hinweise geben einen einfachen Überblick darüber, was mit Ihren personenbezogenen Daten passiert, wenn Sie unsere Website besuchen.</p>

            <h3>2. Allgemeine Hinweise und Pflichtinformationen</h3>
            <h4>Datenschutz</h4>
            <p>Die Betreiber dieser Seiten nehmen den Schutz Ihrer persönlichen Daten sehr ernst. Wir behandeln Ihre personenbezogenen Daten vertraulich und entsprechend der gesetzlichen Datenschutzvorschriften sowie dieser Datenschutzerklärung.</p>

            <h3>3. Datenerfassung auf unserer Website</h3>
            <h4>Wer ist verantwortlich für die Datenerfassung auf dieser Website?</h4>
            <p>Die Datenverarbeitung auf dieser Website erfolgt durch den Websitebetreiber. Dessen Kontaktdaten können Sie dem Impressum dieser Website entnehmen.</p>

            <h4>Wie erfassen wir Ihre Daten?</h4>
            <p>Ihre Daten werden zum einen dadurch erhoben, dass Sie uns diese mitteilen. Hierbei kann es sich z.B. um Daten handeln, die Sie in ein Kontaktformular eingeben.</p>

            <h3>4. Buchungssystem</h3>
            <p>Für die Nutzung unseres Buchungssystems ist eine Registrierung erforderlich. Dabei werden folgende Daten erhoben:</p>
            <ul>
              <li>Name und Vorname</li>
              <li>E-Mail-Adresse</li>
              <li>Buchungsdaten (Datum, Uhrzeit, Platz)</li>
            </ul>
            <p>Diese Daten werden ausschließlich zur Verwaltung der Platzbuchungen verwendet.</p>

            <h3>5. Ihre Rechte</h3>
            <p>Sie haben jederzeit das Recht unentgeltlich Auskunft über Herkunft, Empfänger und Zweck Ihrer gespeicherten personenbezogenen Daten zu erhalten.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const PlaceholderPage = ({ title }) => (
    <div className="page placeholder-page" style={{backgroundImage: "url('/assets/Plätze2.PNG')"}}>
      <div className="content">
        <div className="back-button-container">
          <button className="back-button" onClick={() => navigate('home')}>
            ← Zurück zur Startseite
          </button>
        </div>
        <div className="placeholder-content">
          <h2>{title}</h2>
          <div className="placeholder-info">
            <p>Diese Seite ist noch in Bearbeitung.</p>
            <p>Weitere Informationen folgen in Kürze.</p>
            <p>Bei Fragen wenden Sie sich gerne an uns über die <a href="#" onClick={() => navigate('kontakt')} style={{color: 'var(--primary-red)', textDecoration: 'underline'}}>Kontakt-Seite</a>.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPage = () => {
    switch(currentPage) {
      case 'home':
        return <HomePage />;
      case 'training':
        return <TrainingPage />;
      case 'verein':
        return <VereinPage />;
      case 'verein-anlage':
        return <AnlagePage />;
      case 'mitglied-werden':
        return <PlaceholderPage title="Mitglied werden" />;
      case 'satzung':
        return <PlaceholderPage title="Satzung" />;
      case 'hand-spanndienst':
        return <PlaceholderPage title="Hand- und Spanndienst" />;
      case 'mach-mit':
        return <PlaceholderPage title="Mach Mit!" />;
      case 'kontakt':
        return <KontaktPage />;
      case 'impressum':
        return <ImpressumPage />;
      case 'datenschutz':
        return <DatenschutzPage />;
      default:
        return <HomePage />;
    }
  };

  const Footer = () => (
    <footer className="footer">
      <div className="footer-content">
        <div className="footer-links">
          <a href="#" onClick={() => navigate('impressum')}>Impressum</a>
          <span>|</span>
          <a href="#" onClick={() => navigate('datenschutz')}>Datenschutz</a>
          <span>|</span>
          <a href="#" onClick={() => navigate('kontakt')}>Kontakt</a>
        </div>
        <div className="footer-text">
          <p>&copy; 2024 TC-Wehen e.V. - Alle Rechte vorbehalten</p>
        </div>
      </div>
    </footer>
  );

  return (
    <div className="landing-page">
      <Header />
      <main className="main">
        {renderPage()}
      </main>
      <Footer />
    </div>
  );
};

export default LandingPage;
