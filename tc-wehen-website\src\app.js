// Router state
let currentPage = 'home';

// Booking system state
let bookings = JSON.parse(localStorage.getItem('tcWehen_bookings') || '{}');
let selectedDate = new Date().toISOString().split('T')[0];
let selectedCourt = 1;

// Navigation handler
function navigate(page) {
  currentPage = page;
  
  // Handle external redirects
  if (page === 'medenspiele') {
    window.open('https://htv.liga.nu/cgi-bin/WebObjects/nuLigaTENDE.woa/wa/clubMeetings?club=24972', '_blank');
    return;
  }
  
  if (page === 'mitglied-werden') {
    window.open('http://tc-wehen.de/wp-content/uploads/2025/04/Beitragsordnung_TCWehen_2025.pdf', '_blank');
    return;
  }
  
  if (page === 'satzung') {
    window.open('https://tc-wehen.com/wp-content/uploads/2021/03/Satzung_TC-<PERSON>hen_<PERSON>rz_2020.pdf', '_blank');
    return;
  }
  
  if (page === 'hand-spanndienst') {
    window.open('http://tc-wehen.de/wp-content/uploads/2024/04/TCW_HSD_Arbeitsstunden.pdf', '_blank');
    return;
  }
  
  if (page === 'mach-mit') {
    window.open('http://tc-wehen.de/wp-content/uploads/2024/01/Mithilfe_Mitglieder.pdf', '_blank');
    return;
  }
  
  document.querySelector('#app').innerHTML = createApp();
}

// Header component
function createHeader() {
  return `
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <img src="/assets/Logo-TCW.PNG" alt="TC-Wehen Logo" class="logo-img">
          <h1>TC-Wehen</h1>
        </div>
        <nav class="nav">
          <ul class="nav-list">
            <li><a href="#" onclick="navigate('home')" class="${currentPage === 'home' ? 'active' : ''}">Home</a></li>
            <li><a href="#" onclick="navigate('news')" class="${currentPage === 'news' ? 'active' : ''}">News</a></li>
            <li><a href="#" onclick="navigate('training')" class="${currentPage === 'training' ? 'active' : ''}">Training</a></li>
            <li><a href="#" onclick="navigate('buchung')" class="${currentPage === 'buchung' ? 'active' : ''} booking-button">Platz buchen</a></li>
            <li class="dropdown">
              <a href="#" onclick="navigate('verein')" class="${currentPage.startsWith('verein') ? 'active' : ''}">Verein</a>
              <ul class="dropdown-menu">
                <li><a href="#" onclick="navigate('verein-anlage')">Anlage</a></li>
                <li><a href="#" onclick="navigate('mitglied-werden')">Mitglied werden</a></li>
                <li><a href="#" onclick="navigate('satzung')">Satzung</a></li>
                <li><a href="#" onclick="navigate('hand-spanndienst')">Hand- und Spanndienst</a></li>
                <li><a href="#" onclick="navigate('mach-mit')">Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onclick="navigate('medenspiele')" class="${currentPage === 'medenspiele' ? 'active' : ''}">Medenspiele</a></li>
            <li><a href="#" onclick="navigate('kontakt')" class="${currentPage === 'kontakt' ? 'active' : ''}">Kontakt</a></li>
          </ul>
        </nav>
      </div>
    </header>
  `;
}

// Page components
function createHomePage() {
  return `
    <div class="page home-page" style="background-image: url('/assets/Plätze.PNG')">
      <div class="welcome-message">
        <h2>Herzlich Willkommen beim TC-Wehen!</h2>
      </div>
    </div>
  `;
}

function createNewsPage() {
  return `
    <div class="page news-page" style="background-image: url('/assets/Terrassenansicht-Plätze.PNG')">
      <div class="content">
        <!-- Nur Header Navigation - Seite ist fast leer wie gewünscht -->
      </div>
    </div>
  `;
}

function createTrainingPage() {
  return `
    <div class="page training-page" style="background-image: url('/assets/Plätze3.PNG')">
      <div class="content">
        <div class="training-content">
          <img src="/assets/Tennisschule-Prätorius.PNG" alt="Tennisschule Prätorius" class="training-logo">
          <div class="trainer-placeholder">
            <div class="trainer-image-placeholder">
              [Platzhalter für Bild von Frank Prätorius]
            </div>
            <p class="training-message">Professionelles Tennistraining für alle Altersgruppen und Spielstärken!</p>
            <div class="contact-info">
              <p><strong>Kontakt:</strong></p>
              <p>Email: Beispiel.Frank@Prätorius.TC-Wehen</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createVereinPage() {
  return `
    <div class="page verein-page" style="background-image: url('/assets/Obenansicht-Plätze.PNG')">
      <div class="content">
        <div class="vorstand-section">
          <div class="vorstand-image-placeholder">
            [Platzhalter für Gruppenfoto des Vorstandes]
          </div>
          <div class="vorstand-names">
            <h3>Vorstand</h3>
            <ul>
              <li>1. Vorsitzender: [Name]</li>
              <li>2. Vorsitzender: [Name]</li>
              <li>Kassenwart: [Name]</li>
              <li>Schriftführer: [Name]</li>
              <li>Sportwart: [Name]</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createAnlagePage() {
  return `
    <div class="page anlage-page" style="background-image: url('/assets/Plätze2.PNG')">
      <div class="content">
        <div class="anlage-content">
          <div class="anlage-images">
            <img src="/assets/Plätze.PNG" alt="Tennisplätze" class="anlage-img">
            <img src="/assets/Plätze2.PNG" alt="Tennisplätze 2" class="anlage-img">
            <img src="/assets/Plätze3.PNG" alt="Tennisplätze 3" class="anlage-img">
            <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Terrassenansicht der Plätze" class="anlage-img">
            <img src="/assets/Obenansicht-Plätze.PNG" alt="Obenansicht der Plätze" class="anlage-img">
          </div>
          <div class="anlage-text">
            <p>Hier beim TC-Wehen haben wir 6 schöne und gepflegte Sandplätze und eine Ballwand. Sonne lässt sich bis zum Abend wunderschön auf unserer Terrasse bewundern! Kommt doch mal vorbei!</p>
          </div>
          <div class="maps-container">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2515.123456789!2d8.123456!3d50.123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2sWehen%2C%20Deutschland!5e0!3m2!1sde!2sde!4v1234567890"
              width="100%"
              height="300"
              style="border:0;"
              allowfullscreen=""
              loading="lazy">
            </iframe>
            <p style="margin-top: 1rem; color: var(--text-light); font-style: italic;">
              [Google Maps Einbettung für TC-Wehen - Koordinaten müssen noch angepasst werden]
            </p>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createBuchungPage() {
  return `
    <div class="page buchung-page" style="background-image: url('/assets/Plätze.PNG')">
      <div class="content">
        <div class="booking-content">
          <h2>Platz buchen</h2>
          <div class="booking-container">
            <div class="date-court-selector">
              <div class="date-selector">
                <label for="booking-date">Datum wählen:</label>
                <input type="date" id="booking-date" value="${selectedDate}" onchange="updateSelectedDate(this.value)" min="${new Date().toISOString().split('T')[0]}">
              </div>
              <div class="court-selector">
                <label>Platz wählen:</label>
                <div class="court-buttons">
                  ${Array.from({length: 6}, (_, i) => `
                    <button class="court-btn ${selectedCourt === i + 1 ? 'active' : ''}" onclick="selectCourt(${i + 1})">
                      Platz ${i + 1}
                    </button>
                  `).join('')}
                </div>
              </div>
            </div>
            <div class="time-slots">
              <h3>Verfügbare Zeiten für Platz ${selectedCourt} am ${formatDate(selectedDate)}</h3>
              <div class="slots-grid">
                ${generateTimeSlots()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createKontaktPage() {
  return `
    <div class="page kontakt-page" style="background-image: url('/assets/Ball.PNG')">
      <div class="content">
        <div class="kontakt-content">
          <h2>Kontakt</h2>
          <div class="kontakt-info">
            <p><strong>Telefon:</strong> [Platzhalter Telefonnummer]</p>
            <p><strong>Email:</strong> [Platzhalter Club Email]</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Main app function
export function createApp() {
  let pageContent = '';
  
  switch(currentPage) {
    case 'home':
      pageContent = createHomePage();
      break;
    case 'news':
      pageContent = createNewsPage();
      break;
    case 'training':
      pageContent = createTrainingPage();
      break;
    case 'verein':
      pageContent = createVereinPage();
      break;
    case 'verein-anlage':
      pageContent = createAnlagePage();
      break;
    case 'buchung':
      pageContent = createBuchungPage();
      break;
    case 'kontakt':
      pageContent = createKontaktPage();
      break;
    default:
      pageContent = createHomePage();
  }
  
  return `
    ${createHeader()}
    <main class="main">
      ${pageContent}
    </main>
  `;
}

// Booking system functions
function updateSelectedDate(date) {
  selectedDate = date;
  document.querySelector('#app').innerHTML = createApp();
}

function selectCourt(courtNumber) {
  selectedCourt = courtNumber;
  document.querySelector('#app').innerHTML = createApp();
}

function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('de-DE', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function generateTimeSlots() {
  const slots = [];
  const today = new Date().toISOString().split('T')[0];
  const currentHour = new Date().getHours();

  for (let hour = 9; hour <= 20; hour++) {
    const timeSlot = `${hour.toString().padStart(2, '0')}:00`;
    const bookingKey = `${selectedDate}_${selectedCourt}_${timeSlot}`;
    const isBooked = bookings[bookingKey];
    const isPast = selectedDate === today && hour <= currentHour;
    const isDisabled = isBooked || isPast;

    slots.push(`
      <div class="time-slot ${isDisabled ? 'disabled' : 'available'}"
           onclick="${isDisabled ? '' : `openBookingModal('${timeSlot}')`}">
        <div class="time">${timeSlot} - ${(hour + 1).toString().padStart(2, '0')}:00</div>
        <div class="status">
          ${isPast ? 'Abgelaufen' : isBooked ? `Gebucht: ${isBooked.player1}${isBooked.player2 ? ' & ' + isBooked.player2 : ''}` : 'Verfügbar'}
        </div>
      </div>
    `);
  }

  return slots.join('');
}

function openBookingModal(timeSlot) {
  const modal = document.createElement('div');
  modal.className = 'booking-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>Platz ${selectedCourt} buchen</h3>
        <span class="close-modal" onclick="closeBookingModal()">&times;</span>
      </div>
      <div class="modal-body">
        <p><strong>Datum:</strong> ${formatDate(selectedDate)}</p>
        <p><strong>Zeit:</strong> ${timeSlot} - ${(parseInt(timeSlot) + 1).toString().padStart(2, '0')}:00</p>
        <p><strong>Platz:</strong> ${selectedCourt}</p>

        <form onsubmit="submitBooking(event, '${timeSlot}')">
          <div class="form-group">
            <label for="player1">Spieler 1 (Pflicht):</label>
            <input type="text" id="player1" required placeholder="Name eingeben" autocomplete="name">
          </div>
          <div class="form-group">
            <label for="player2">Spieler 2 (Optional):</label>
            <input type="text" id="player2" placeholder="Name eingeben (optional)" autocomplete="name">
          </div>
          <div class="form-actions">
            <button type="button" onclick="closeBookingModal()">Abbrechen</button>
            <button type="submit">Buchen</button>
          </div>
        </form>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Mobile optimizations
  if (window.innerWidth <= 480) {
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';

    // Focus first input for better mobile UX
    setTimeout(() => {
      const firstInput = document.getElementById('player1');
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);

    // Add swipe down to close functionality
    let startY = 0;
    const modalContent = modal.querySelector('.modal-content');

    modalContent.addEventListener('touchstart', (e) => {
      startY = e.touches[0].clientY;
    });

    modalContent.addEventListener('touchmove', (e) => {
      const currentY = e.touches[0].clientY;
      const diff = currentY - startY;

      if (diff > 0) {
        modalContent.style.transform = `translateY(${Math.min(diff, 100)}px)`;
      }
    });

    modalContent.addEventListener('touchend', (e) => {
      const currentY = e.changedTouches[0].clientY;
      const diff = currentY - startY;

      if (diff > 100) {
        closeBookingModal();
      } else {
        modalContent.style.transform = 'translateY(0)';
      }
    });
  }

  // Close modal when clicking outside
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeBookingModal();
    }
  });
}

function closeBookingModal() {
  const modal = document.querySelector('.booking-modal');
  if (modal) {
    // Restore body scroll
    document.body.style.overflow = '';

    // Smooth close animation for mobile
    if (window.innerWidth <= 480) {
      const modalContent = modal.querySelector('.modal-content');
      modalContent.style.transform = 'translateY(100%)';
      modalContent.style.transition = 'transform 0.3s ease';

      setTimeout(() => {
        modal.remove();
      }, 300);
    } else {
      modal.remove();
    }
  }
}

function submitBooking(event, timeSlot) {
  event.preventDefault();

  const player1 = document.getElementById('player1').value.trim();
  const player2 = document.getElementById('player2').value.trim();

  if (!player1) {
    alert('Bitte geben Sie mindestens einen Spielernamen ein.');
    return;
  }

  const bookingKey = `${selectedDate}_${selectedCourt}_${timeSlot}`;
  bookings[bookingKey] = {
    player1: player1,
    player2: player2 || null,
    date: selectedDate,
    court: selectedCourt,
    time: timeSlot,
    bookedAt: new Date().toISOString()
  };

  localStorage.setItem('tcWehen_bookings', JSON.stringify(bookings));

  closeBookingModal();
  document.querySelector('#app').innerHTML = createApp();

  alert(`Platz ${selectedCourt} erfolgreich gebucht für ${formatDate(selectedDate)} um ${timeSlot}!`);
}

// Make functions global
window.navigate = navigate;
window.updateSelectedDate = updateSelectedDate;
window.selectCourt = selectCourt;
window.openBookingModal = openBookingModal;
window.closeBookingModal = closeBookingModal;
window.submitBooking = submitBooking;
