/* TC-<PERSON>hen Website Styles */
:root {
  --primary-red: #d32f2f;
  --primary-orange: #ff6b35;
  --primary-white: #ffffff;
  --text-dark: #333333;
  --text-light: #666666;
  --shadow: rgba(0, 0, 0, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--primary-white);
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, var(--primary-red), var(--primary-orange));
  color: var(--primary-white);
  padding: 1rem 0;
  box-shadow: 0 2px 10px var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-img {
  height: 50px;
  width: auto;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: bold;
}

/* Navigation Styles */
.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-list a {
  color: var(--primary-white);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.nav-list a:hover,
.nav-list a.active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--primary-white);
  min-width: 200px;
  box-shadow: 0 5px 15px var(--shadow);
  border-radius: 5px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  padding: 0.5rem 0;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu a {
  color: var(--text-dark);
  display: block;
  padding: 0.7rem 1rem;
  border-radius: 0;
}

.dropdown-menu a:hover {
  background-color: #f5f5f5;
  transform: none;
}

/* Booking Button Styling */
.booking-button {
  background: linear-gradient(45deg, #ff6b35, #d32f2f) !important;
  font-weight: bold !important;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.booking-button:hover {
  background: linear-gradient(45deg, #d32f2f, #ff6b35) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5) !important;
  transform: translateY(-3px) !important;
}

/* Touch and Mobile Improvements */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input, textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Improved touch targets */
.nav-list a,
.court-btn,
.time-slot,
.form-actions button {
  min-height: 44px;
  min-width: 44px;
}

/* Main Content */
.main {
  flex: 1;
}

.page {
  min-height: calc(100vh - 80px);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  box-shadow: 0 10px 30px var(--shadow);
}
/* Home Page */
.welcome-message {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 3rem;
  border-radius: 15px;
  box-shadow: 0 15px 35px var(--shadow);
}

.welcome-message h2 {
  font-size: 3rem;
  color: var(--primary-red);
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px var(--shadow);
}

/* Training Page */
.training-content {
  text-align: center;
}

.training-logo {
  max-width: 300px;
  height: auto;
  margin-bottom: 2rem;
}

.trainer-image-placeholder {
  width: 200px;
  height: 250px;
  background: #f0f0f0;
  border: 2px dashed var(--text-light);
  margin: 2rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  color: var(--text-light);
}

.training-message {
  font-size: 1.2rem;
  margin: 2rem 0;
  color: var(--primary-red);
  font-weight: 500;
}

.contact-info {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 10px;
  margin-top: 2rem;
}

/* Verein Page */
.vorstand-section {
  text-align: center;
}

.vorstand-image-placeholder {
  width: 100%;
  height: 300px;
  background: #f0f0f0;
  border: 2px dashed var(--text-light);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  color: var(--text-light);
  font-size: 1.1rem;
}

.vorstand-names {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.vorstand-names h3 {
  color: var(--primary-red);
  margin-bottom: 1rem;
  text-align: center;
}

.vorstand-names ul {
  list-style: none;
}

.vorstand-names li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

/* Anlage Page */
.anlage-content {
  text-align: center;
}

.anlage-images {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 250px);
  gap: 1.5rem;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.anlage-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 8px 25px var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.anlage-img:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Spezielle Anordnung für die 5 Bilder */
.anlage-img:nth-child(1) {
  grid-column: 1;
  grid-row: 1;
}

.anlage-img:nth-child(2) {
  grid-column: 2;
  grid-row: 1;
}

.anlage-img:nth-child(3) {
  grid-column: 1;
  grid-row: 2;
}

.anlage-img:nth-child(4) {
  grid-column: 2;
  grid-row: 2;
}

.anlage-img:nth-child(5) {
  grid-column: 1 / 3;
  grid-row: 3;
  height: 200px;
}

.image-placeholder {
  width: 100%;
  height: 200px;
  background: #f0f0f0;
  border: 2px dashed var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  color: var(--text-light);
}

.anlage-text {
  font-size: 1.2rem;
  margin: 2rem 0;
  color: var(--primary-red);
  font-weight: 500;
}

.maps-container {
  margin-top: 2rem;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow);
}

/* Booking Page */
.booking-content {
  text-align: center;
}

.booking-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.booking-container {
  max-width: 1000px;
  margin: 0 auto;
}

.date-court-selector {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  justify-content: center;
  flex-wrap: wrap;
}

.date-selector, .court-selector {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px var(--shadow);
}

.date-selector label, .court-selector label {
  display: block;
  margin-bottom: 1rem;
  font-weight: bold;
  color: var(--primary-red);
}

.date-selector input {
  padding: 0.8rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  min-width: 200px;
}

.court-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.court-btn {
  padding: 0.8rem 1rem;
  border: 2px solid var(--primary-orange);
  background: white;
  color: var(--primary-orange);
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.court-btn:hover {
  background: var(--primary-orange);
  color: white;
  transform: translateY(-2px);
}

.court-btn.active {
  background: var(--primary-red);
  border-color: var(--primary-red);
  color: white;
}

.time-slots h3 {
  color: var(--primary-red);
  margin-bottom: 1.5rem;
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.time-slot {
  background: white;
  border: 2px solid #ddd;
  border-radius: 10px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.time-slot.available {
  border-color: var(--primary-orange);
}

.time-slot.available:hover {
  border-color: var(--primary-red);
  background: #f0f8ff;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px var(--shadow);
}

.time-slot.disabled {
  background: #f5f5f5;
  border-color: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.time-slot .time {
  font-weight: bold;
  color: var(--primary-red);
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.time-slot .status {
  color: var(--text-light);
  font-size: 0.9rem;
}

.time-slot.disabled .time {
  color: #999;
}

/* Booking Modal */
.booking-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-red), var(--primary-orange));
  color: white;
  padding: 1.5rem;
  border-radius: 15px 15px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.close-modal {
  font-size: 2rem;
  cursor: pointer;
  line-height: 1;
  padding: 0.2rem 0.5rem;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.close-modal:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
}

.modal-body p {
  margin: 0.5rem 0;
  color: var(--text-dark);
}

.form-group {
  margin: 1.5rem 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: var(--primary-red);
}

.form-group input {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-orange);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.form-actions button {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-actions button[type="button"] {
  background: #6c757d;
  color: white;
}

.form-actions button[type="button"]:hover {
  background: #5a6268;
}

.form-actions button[type="submit"] {
  background: linear-gradient(45deg, var(--primary-orange), var(--primary-red));
  color: white;
  font-weight: bold;
}

.form-actions button[type="submit"]:hover {
  background: linear-gradient(45deg, var(--primary-red), var(--primary-orange));
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
}

/* Kontakt Page */
.kontakt-content {
  text-align: center;
}

.kontakt-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
}

.kontakt-info {
  background: #f9f9f9;
  padding: 2rem;
  border-radius: 10px;
  max-width: 400px;
  margin: 0 auto;
}

.kontakt-info p {
  margin: 1rem 0;
  font-size: 1.1rem;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
  }

  .nav-list {
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
  }

  .nav-list a {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .booking-button {
    order: -1;
    width: 100%;
    text-align: center;
    margin-bottom: 0.5rem;
    padding: 0.8rem 1rem !important;
    font-size: 1.1rem !important;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .content {
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 15px;
  }

  /* Mobile Booking Optimizations */
  .booking-content h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .date-court-selector {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .date-selector, .court-selector {
    padding: 1rem;
  }

  .date-selector input {
    min-width: 100%;
    font-size: 1.1rem;
    padding: 1rem;
  }

  .court-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }

  .court-btn {
    padding: 1rem;
    font-size: 1rem;
    min-height: 50px;
  }

  .slots-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .time-slot {
    padding: 1.2rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .time-slot .time {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
  }

  .time-slot .status {
    font-size: 1rem;
  }

  .anlage-images {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(5, 200px);
    max-width: 100%;
  }

  .anlage-img:nth-child(5) {
    grid-column: 1;
    grid-row: 5;
    height: 200px;
  }
}

@media (max-width: 480px) {
  .anlage-images {
    gap: 1rem;
    grid-template-rows: repeat(5, 180px);
  }

  .logo h1 {
    font-size: 1.4rem;
  }

  .nav-list {
    font-size: 0.8rem;
    gap: 0.3rem;
  }

  .nav-list a {
    padding: 0.3rem 0.6rem;
  }

  .booking-button {
    font-size: 1rem !important;
    padding: 0.7rem !important;
  }

  /* Ultra Mobile Booking Optimizations */
  .court-buttons {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .court-btn {
    padding: 1.2rem;
    font-size: 1.1rem;
    min-height: 60px;
  }

  .time-slot {
    padding: 1.5rem;
    min-height: 90px;
  }

  .time-slot .time {
    font-size: 1.3rem;
  }

  .time-slot .status {
    font-size: 1.1rem;
  }

  /* Mobile Modal Optimizations */
  .booking-modal {
    padding: 1rem;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .modal-content {
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 20px 20px 0 0;
    position: fixed;
    bottom: 0;
    max-height: 85vh;
  }

  .modal-header {
    padding: 1rem 1.5rem;
    border-radius: 20px 20px 0 0;
  }

  .modal-header h3 {
    font-size: 1.3rem;
  }

  .close-modal {
    font-size: 1.8rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .form-group input {
    padding: 1rem;
    font-size: 1.1rem;
    border-width: 2px;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .form-actions button {
    padding: 1rem;
    font-size: 1.1rem;
    min-height: 50px;
  }

  /* Touch-friendly improvements */
  .date-selector input {
    padding: 1.2rem;
    font-size: 1.2rem;
  }

  .booking-content h2 {
    font-size: 1.5rem;
  }

  .time-slots h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
}
