import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { Calendar, Clock, User, Plus, X } from 'lucide-react'

const BookingCalendar = () => {
  const { user } = useAuth()
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [courts, setCourts] = useState([])
  const [bookings, setBookings] = useState([])
  const [loading, setLoading] = useState(true)
  const [showBookingForm, setShowBookingForm] = useState(false)
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [bookingForm, setBookingForm] = useState({
    player1_name: '',
    player2_name: ''
  })

  // Zeitslots von 9:00 bis 21:00
  const timeSlots = []
  for (let hour = 9; hour <= 20; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`)
  }

  useEffect(() => {
    fetchCourts()
  }, [])

  useEffect(() => {
    if (selectedDate) {
      fetchBookings()
    }
  }, [selectedDate])

  const fetchCourts = async () => {
    try {
      const { data, error } = await supabase
        .from('courts')
        .select('*')
        .eq('is_active', true)
        .order('id')

      if (error) throw error
      setCourts(data || [])
    } catch (error) {
      console.error('Error fetching courts:', error)
    }
  }

  const fetchBookings = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          courts (
            id,
            name,
            description
          )
        `)
        .eq('booking_date', selectedDate)
        .eq('status', 'confirmed')
        .order('start_time')

      if (error) throw error
      setBookings(data || [])
    } catch (error) {
      console.error('Error fetching bookings:', error)
    } finally {
      setLoading(false)
    }
  }

  const isSlotBooked = (courtId, timeSlot) => {
    return bookings.some(booking => 
      booking.court_id === courtId && booking.start_time === timeSlot + ':00'
    )
  }

  const getBookingForSlot = (courtId, timeSlot) => {
    return bookings.find(booking => 
      booking.court_id === courtId && booking.start_time === timeSlot + ':00'
    )
  }

  const handleSlotClick = (courtId, timeSlot) => {
    if (isSlotBooked(courtId, timeSlot)) return
    
    // Prüfe ob das Datum in der Vergangenheit liegt
    const slotDateTime = new Date(`${selectedDate}T${timeSlot}:00`)
    if (slotDateTime < new Date()) {
      alert('Buchungen in der Vergangenheit sind nicht möglich')
      return
    }

    setSelectedSlot({ courtId, timeSlot })
    setShowBookingForm(true)
  }

  const handleBooking = async (e) => {
    e.preventDefault()
    if (!selectedSlot || !bookingForm.player1_name) return

    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session?.access_token) {
        alert('Sie müssen angemeldet sein')
        return
      }

      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/bookings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.session.access_token}`
        },
        body: JSON.stringify({
          court_id: selectedSlot.courtId,
          booking_date: selectedDate,
          start_time: selectedSlot.timeSlot + ':00',
          end_time: (parseInt(selectedSlot.timeSlot) + 1).toString().padStart(2, '0') + ':00:00',
          player1_name: bookingForm.player1_name,
          player2_name: bookingForm.player2_name || null
        })
      })

      if (response.ok) {
        setShowBookingForm(false)
        setSelectedSlot(null)
        setBookingForm({ player1_name: '', player2_name: '' })
        fetchBookings()
        alert('Buchung erfolgreich!')
      } else {
        const error = await response.json()
        alert(error.error || 'Fehler bei der Buchung')
      }
    } catch (error) {
      console.error('Booking error:', error)
      alert('Fehler bei der Buchung')
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('de-DE', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="container" style={{ padding: '2rem 1rem' }}>
      <div className="mb-6">
        <h1 style={{ marginBottom: '1rem' }}>Platz buchen</h1>
        
        <div className="form-group" style={{ maxWidth: '300px' }}>
          <label className="form-label">
            <Calendar size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
            Datum auswählen
          </label>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="form-input"
            min={new Date().toISOString().split('T')[0]}
          />
        </div>
        
        <p style={{ color: 'var(--gray-600)', marginTop: '0.5rem' }}>
          {formatDate(selectedDate)}
        </p>
      </div>

      {loading ? (
        <div className="text-center">
          <p>Lade Buchungen...</p>
        </div>
      ) : (
        <div className="card">
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '2px solid var(--gray-200)' }}>
                    Zeit
                  </th>
                  {courts.map(court => (
                    <th key={court.id} style={{ 
                      padding: '1rem', 
                      textAlign: 'center', 
                      borderBottom: '2px solid var(--gray-200)',
                      minWidth: '150px'
                    }}>
                      {court.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {timeSlots.map(timeSlot => (
                  <tr key={timeSlot}>
                    <td style={{ 
                      padding: '0.75rem 1rem', 
                      borderBottom: '1px solid var(--gray-200)',
                      fontWeight: '500'
                    }}>
                      <Clock size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                      {timeSlot}
                    </td>
                    {courts.map(court => {
                      const isBooked = isSlotBooked(court.id, timeSlot)
                      const booking = getBookingForSlot(court.id, timeSlot)
                      const isPast = new Date(`${selectedDate}T${timeSlot}:00`) < new Date()
                      
                      return (
                        <td key={court.id} style={{ 
                          padding: '0.5rem', 
                          borderBottom: '1px solid var(--gray-200)',
                          textAlign: 'center'
                        }}>
                          {isBooked ? (
                            <div style={{
                              background: 'var(--gradient-primary)',
                              color: 'white',
                              padding: '0.5rem',
                              borderRadius: '6px',
                              fontSize: '0.875rem'
                            }}>
                              <div style={{ fontWeight: '500' }}>
                                {booking?.player1_name}
                              </div>
                              {booking?.player2_name && (
                                <div>vs {booking.player2_name}</div>
                              )}
                            </div>
                          ) : (
                            <button
                              onClick={() => handleSlotClick(court.id, timeSlot)}
                              disabled={isPast}
                              style={{
                                background: isPast ? 'var(--gray-200)' : 'var(--gray-100)',
                                border: '1px dashed var(--gray-300)',
                                borderRadius: '6px',
                                padding: '0.5rem',
                                cursor: isPast ? 'not-allowed' : 'pointer',
                                width: '100%',
                                color: isPast ? 'var(--gray-400)' : 'var(--gray-600)',
                                transition: 'all 0.2s'
                              }}
                              onMouseEnter={(e) => {
                                if (!isPast) {
                                  e.target.style.background = 'var(--primary-red)'
                                  e.target.style.color = 'white'
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!isPast) {
                                  e.target.style.background = 'var(--gray-100)'
                                  e.target.style.color = 'var(--gray-600)'
                                }
                              }}
                            >
                              <Plus size={16} />
                            </button>
                          )}
                        </td>
                      )
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '1rem'
        }}>
          <div className="card" style={{ maxWidth: '400px', width: '100%' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <h3>Platz buchen</h3>
              <button
                onClick={() => setShowBookingForm(false)}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: 'var(--gray-400)'
                }}
              >
                <X size={24} />
              </button>
            </div>

            <div style={{ marginBottom: '1rem', color: 'var(--gray-600)' }}>
              <p><strong>Datum:</strong> {formatDate(selectedDate)}</p>
              <p><strong>Zeit:</strong> {selectedSlot?.timeSlot}</p>
              <p><strong>Platz:</strong> {courts.find(c => c.id === selectedSlot?.courtId)?.name}</p>
            </div>

            <form onSubmit={handleBooking}>
              <div className="form-group">
                <label className="form-label">
                  <User size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                  Spieler 1 (Pflicht)
                </label>
                <input
                  type="text"
                  value={bookingForm.player1_name}
                  onChange={(e) => setBookingForm({
                    ...bookingForm,
                    player1_name: e.target.value
                  })}
                  className="form-input"
                  placeholder="Name des ersten Spielers"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  <User size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                  Spieler 2 (Optional)
                </label>
                <input
                  type="text"
                  value={bookingForm.player2_name}
                  onChange={(e) => setBookingForm({
                    ...bookingForm,
                    player2_name: e.target.value
                  })}
                  className="form-input"
                  placeholder="Name des zweiten Spielers"
                />
              </div>

              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  type="button"
                  onClick={() => setShowBookingForm(false)}
                  className="btn btn-outline"
                  style={{ flex: 1 }}
                >
                  Abbrechen
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  style={{ flex: 1 }}
                >
                  Buchen
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default BookingCalendar
